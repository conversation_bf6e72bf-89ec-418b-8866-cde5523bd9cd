const moment = require('moment');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const NewConnectionsMetric = require('../models/new-connections-metric');
const socialProofCacheLib = require('./social-proof-cache')
const _ = require('underscore');

let cachedTime;
let cachedImages = [];

let verificationMaleCachedTime;
let verificationMaleCachedImages = [];
let verificationFemaleCachedTime;
let verificationFemaleCachedImages = [];
let verificationNonBinaryCachedTime;
let verificationNonBinaryCachedImages = [];

let superLikeCachedTime;
let superLikeCachedImages = [];

let coinPurchaseCachedTime;
let coinPurchaseCachedImages = [];

let neuronPurchaseCachedTime;
let neuronPurchaseCachedImages = [];

let boostPurchaseCachedTime;
let boostPurchaseCachedImages = [];

async function getSocialProofRouteHandler(req, res, next) {

  if (cachedImages.length == 0 || moment().diff(cachedTime, 'hours') >= 1) {
    cachedTime = Date.now();
    cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('cachedImages')
  }

  res.json({
    images: cachedImages,
  });
}

async function getSocialProofVerificationRouteHandler(req, res, next) {

  if (verificationMaleCachedImages.length == 0 || moment().diff(verificationMaleCachedTime, 'hours') >= 1) {
    verificationMaleCachedTime = Date.now();
    verificationMaleCachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationMaleCachedImages')
  }

  if (verificationFemaleCachedImages.length == 0 || moment().diff(verificationFemaleCachedTime, 'hours') >= 1) {
    verificationFemaleCachedTime = Date.now();
    verificationFemaleCachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationFemaleCachedImages')
  }

  if (verificationNonBinaryCachedImages.length == 0 || moment().diff(verificationNonBinaryCachedTime, 'hours') >= 1) {
    verificationNonBinaryCachedTime = Date.now();
    verificationNonBinaryCachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationNonBinaryCachedImages')
  }

  let preference = 'dating'
  if(req.user.preferences.dating.length === 0 && req.user.preferences.friends.length > 0){
    preference = 'friends'
  }

  let result = []
  if(req.user.preferences[preference].includes('male')){
    result = result.concat(verificationMaleCachedImages);
  }
  if(req.user.preferences[preference].includes('female')){
    result = result.concat(verificationFemaleCachedImages);
  }
  if(req.user.preferences[preference].includes('non-binary')){
    result = result.concat(verificationNonBinaryCachedImages);
  }

  result = _.shuffle(result);

  res.json({
    images: [...result],
  });
}

async function getSuperLikeSocialProofRouteHandler(req, res, next) {
  if (superLikeCachedImages.length == 0 || moment().diff(superLikeCachedTime, 'hours') >= 1) {
    superLikeCachedTime = Date.now();
    superLikeCachedImages = await socialProofCacheLib.getCachedImagesSocialProof('superLikeCachedImages')
  }

  res.json({
    images: superLikeCachedImages,
  });
}

async function getCoinPurchaseSocialProofRouteHandler(req, res, next) {
  if (coinPurchaseCachedImages.length == 0 || moment().diff(coinPurchaseCachedTime, 'hours') >= 1) {
    coinPurchaseCachedTime = Date.now();

    const receipts = await CoinPurchaseReceipt
      .find()
      .sort('-purchaseDate')
      .limit(20)
      .populate('user', 'pictures');

    coinPurchaseCachedImages = receipts
      .filter((x) => x.user && x.user.pictures.length > 0)
      .map((x) => x.user.pictures[0]);

    // remove duplicates
    coinPurchaseCachedImages = await socialProofCacheLib.getCachedImagesSocialProof('coinPurchaseCachedImages') 
  }

  res.json({
    images: coinPurchaseCachedImages,
  });
}

async function getNeuronPurchaseSocialProofRouteHandler(req, res, next) {
  if (neuronPurchaseCachedImages.length == 0 || moment().diff(neuronPurchaseCachedTime, 'hours') >= 1) {
    neuronPurchaseCachedTime = Date.now();
    neuronPurchaseCachedImages = await socialProofCacheLib.getCachedImagesSocialProof('neuronPurchaseCachedImages') 
  }

  res.json({
    images: neuronPurchaseCachedImages,
  });
}

async function getBoostPurchaseSocialProofRouteHandler(req, res, next) {
  if (boostPurchaseCachedImages.length == 0 || moment().diff(boostPurchaseCachedTime, 'hours') >= 1) {
    boostPurchaseCachedTime = Date.now();
    boostPurchaseCachedImages = await socialProofCacheLib.getCachedImagesSocialProof('boostPurchaseCachedImages') 
  }

  res.json({
    images: boostPurchaseCachedImages,
  });
}

async function getNewConnectionsCount() {
  try {
    const count = await NewConnectionsMetric.getNewConnectionsLast7Days();
    return count;
  } catch (error) {
    console.error('Error fetching new connections count:', error);
    return 0;
  }
}

module.exports = {
  getSocialProofRouteHandler,
  getSuperLikeSocialProofRouteHandler,
  getCoinPurchaseSocialProofRouteHandler,
  getNeuronPurchaseSocialProofRouteHandler,
  getBoostPurchaseSocialProofRouteHandler,
  getSocialProofVerificationRouteHandler,
  getNewConnectionsCount
};
