const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const socialProofLib = require('../lib/social-proof');
const socialLib = require('../lib/social');

module.exports = function () {
  router.get('/', asyncHandler(socialProofLib.getSocialProofRouteHandler));
  router.get('/super-like', async<PERSON>andler(socialProofLib.getSuperLikeSocialProofRouteHandler));
  router.get('/coin-purchase', asyncHandler(socialProofLib.getCoinPurchaseSocialProofRouteHandler));
  router.get('/neuron-purchase', asyncHandler(socialProofLib.getNeuronPurchaseSocialProofRouteHandler));
  router.get('/boost-purchase', asyncHandler(socialProofLib.getBoostPurchaseSocialProofRouteHandler));
  router.get('/verification', asyncHandler(socialProofLib.getSocialProofVerificationRouteHandler));

  router.get('/testimonials', asyncHandler(async (req, res, next) => {
    const testimonials = await socialLib.getTestimonials(req.user);
    res.json({
      testimonials,
    });
  }));

  router.get('/new-connections', asyncHandler(async (req, res, next) => {
    const newConnectionsCount = await socialProofLib.getNewConnectionsCount();
    res.json({
      newConnectionsCount,
    });
  }));

  return router;
};
