const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema(
  {
    hour: { type: Date, required: true, unique: true },
    numNewConnections: { type: Number, default: 0 },
    createdAt: { type: Date, default: Date.now },
  },
  {
    versionKey: false,
  }
);

schema.index({ hour: -1 });

schema.statics.increment = async function (metric = 'numNewConnections', n = 1) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const update = {
    $inc: {
      [metric]: n,
    },
  };

  await this.updateOne(
    { date: today },
    update,
    { upsert: true },
  );
}

schema.statics.getNewConnectionsLast7Days = async function () {
  const now = new Date();
  const sevenDaysAgo = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));

  const result = await this.aggregate([
    {
      $match: {
        hour: { $gte: sevenDaysAgo }
      }
    },
    {
      $group: {
        _id: null,
        totalConnections: { $sum: '$numNewConnections' }
      }
    }
  ]);

  return result.length > 0 ? result[0].totalConnections : 0;
};

const connection = connectionLib.getEventsConnection() || mongoose;
module.exports = connection.model('NewConnectionsMetric', schema);
