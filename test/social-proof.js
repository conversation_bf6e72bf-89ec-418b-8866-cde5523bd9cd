const { expect } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const constants = require('../lib/constants');
const basic = require('../lib/basic');
const { app, validImagePath } = require('./common');
const socialProofCacheLib = require('../lib/social-proof-cache')
const SocialQueryCache = require('../models/social-query-cache')
const PurchaseReceipt = require('../models/purchase-receipt');
const SuperLikePurchaseReceipt = require('../models/super-like-purchase-receipt');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const NeuronPurchaseReceipt = require('../models/neuron-purchase-receipt');
const BoostPurchaseReceipt = require('../models/boost-purchase-receipt')
const User = require('../models/user')
const NewConnectionsMetric = require('../models/new-connections-metric')
const Chat = require('../models/chat')

describe('set cache for social proofs', () => {
  
  before(async () => {
    constants.enforceVerification.restore();
    sinon.stub(constants, 'enforceVerification').returns(true);
    // create male
    let usersPictureId = []
    for (let i = 1; i <= 2; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.81' });
      expect(res.status).to.equal(200);

      let user = await User.findById(i);
      user.gender = 'male'
      await user.save();

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', i)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
      usersPictureId[i] = res.body.pictures[0];

      fakeS3.getObject = function (params) {
        const impl = function (resolve, reject) {
          resolve({
            Body: Buffer.from('mock file content'),
            Metadata: { 'mock-metadata': 'value' },
          });
        };
        return {
          promise: () => new Promise(impl),
        };
      };

      const payload = {
        img: 'base-64-image-data',
        secure: {
          version: "2.7.0",
          token: "token-data",
          verification: "verification-data",
          signature: "signature-data",
        },
      };

      res = await request(app)
        .post('/v1/user/profileVerificationPicture/liveness')
        .set('authorization', i)
        .send(payload);
      expect(res.status).to.equal(200);
      expect(res.body.verificationStatus).to.equal('verified');

    }

    //create female
    for (let i = 3; i <= 4; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.81' });
      expect(res.status).to.equal(200);

      let user = await User.findById(i);
      // user.verification.status = i % 2 === 0 ? 'verified' : 'unverified';
      user.gender = 'female'
      await user.save();

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', i)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
      usersPictureId[i] = res.body.pictures[0];

      fakeS3.getObject = function (params) {
        const impl = function (resolve, reject) {
          resolve({
            Body: Buffer.from('mock file content'),
            Metadata: { 'mock-metadata': 'value' },
          });
        };
        return {
          promise: () => new Promise(impl),
        };
      };

      const payload = {
        img: 'base-64-image-data',
        secure: {
          version: "2.7.0",
          token: "token-data",
          verification: "verification-data",
          signature: "signature-data",
        },
      };

      res = await request(app)
        .post('/v1/user/profileVerificationPicture/liveness')
        .set('authorization', i)
        .send(payload);
      expect(res.status).to.equal(200);
      expect(res.body.verificationStatus).to.equal('verified');
    }

    //create non-binary
    for (let i = 5; i <= 6; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.81' });
      expect(res.status).to.equal(200);

      let user = await User.findById(i);
      // user.verification.status = i % 2 === 0 ? 'verified' : 'unverified';
      user.gender = 'non-binary'
      await user.save();

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', i)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
      usersPictureId[i] = res.body.pictures[0];

      fakeS3.getObject = function (params) {
        const impl = function (resolve, reject) {
          resolve({
            Body: Buffer.from('mock file content'),
            Metadata: { 'mock-metadata': 'value' },
          });
        };
        return {
          promise: () => new Promise(impl),
        };
      };

      const payload = {
        img: 'base-64-image-data',
        secure: {
          version: "2.7.0",
          token: "token-data",
          verification: "verification-data",
          signature: "signature-data",
        },
      };

      res = await request(app)
        .post('/v1/user/profileVerificationPicture/liveness')
        .set('authorization', i)
        .send(payload);
      expect(res.status).to.equal(200);
      expect(res.body.verificationStatus).to.equal('verified');
    }

  });

  beforeEach(async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);
  })

  it('set cache for social proofs', async () => {
      await User.updateMany(
        { _id: { $in: [1, 3, 5] } },
        { $set: { 'verification.status': 'pending' } }
      );
    
      await Promise.all([
        new PurchaseReceipt({ user: '2', transactionId: '1', purchaseDate: Date.now() }).save(),
        new SuperLikePurchaseReceipt({ user: '4', transactionId: '1', purchaseDate: Date.now() }).save(),
        new CoinPurchaseReceipt({ user: '6', transactionId: '1', purchaseDate: Date.now() }).save(),
        new NeuronPurchaseReceipt({ user: '2', transactionId: '1', purchaseDate: Date.now() }).save(),
        new BoostPurchaseReceipt({ user: '4', transactionId: '1', purchaseDate: Date.now() }).save()
      ]);
    
      let res = await request(app)
        .post('/v1/worker/setCacheImagesSocialProof')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    
      let cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('cachedImages');
      expect(cachedImages.length).to.equal(1);
      let userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationMaleCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationFemaleCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationNonBinaryCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([6]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('superLikeCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('coinPurchaseCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([6]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('neuronPurchaseCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('boostPurchaseCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4]);
    
      await User.updateMany(
        { _id: { $in: [1, 3, 5] } },
        { $set: { 'verification.status': 'verified' } }
      );
    
      await Promise.all([
        new PurchaseReceipt({ user: '1', transactionId: '2', purchaseDate: Date.now() }).save(),
        new SuperLikePurchaseReceipt({ user: '3', transactionId: '2', purchaseDate: Date.now() }).save(),
        new CoinPurchaseReceipt({ user: '5', transactionId: '2', purchaseDate: Date.now() }).save(),
        new NeuronPurchaseReceipt({ user: '1', transactionId: '2', purchaseDate: Date.now() }).save(),
        new BoostPurchaseReceipt({ user: '3', transactionId: '2', purchaseDate: Date.now() }).save()
      ]);
    
      res = await request(app)
        .post('/v1/worker/setCacheImagesSocialProof')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('cachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2, 1]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationMaleCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2, 1]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationFemaleCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4, 3]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationNonBinaryCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([6, 5]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('superLikeCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4, 3]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('coinPurchaseCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([6, 5]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('neuronPurchaseCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2, 1]);
    
      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('boostPurchaseCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4, 3]);
    
      // Checking if routes are working
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.81' });
      expect(res.status).to.equal(200);
    
      let user = await User.findById(0);
      user.gender = 'male';
      user.preferences.dating = ['female'];
      await user.save();
    
      res = await request(app)
        .get('/v1/social-proof/verification')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    
      console.log('res: ', res.body);
    
      userIdsAsNumbers = res.body.images.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4, 3]);
    });

})

describe('New Connections Metric', () => {
  beforeEach(async () => {
    // Clean up any existing metrics
    await NewConnectionsMetric.deleteMany({});
  });

  it('should increment new connections metric', async () => {
    // Test the increment function
    await NewConnectionsMetric.increment();
    await NewConnectionsMetric.increment(3);

    const currentHour = new Date();
    currentHour.setMinutes(0, 0, 0);

    const metric = await NewConnectionsMetric.findOne({ hour: currentHour });
    expect(metric).to.exist;
    expect(metric.numNewConnections).to.equal(4);
  });

  it('should get new connections count for last 7 days', async () => {
    const now = new Date();

    // Create metrics for different hours
    const metrics = [
      { hour: new Date(now.getTime() - 1 * 60 * 60 * 1000), numNewConnections: 10 }, // 1 hour ago
      { hour: new Date(now.getTime() - 25 * 60 * 60 * 1000), numNewConnections: 15 }, // 1 day ago
      { hour: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000), numNewConnections: 20 }, // 3 days ago
      { hour: new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000), numNewConnections: 25 }, // 6 days ago
      { hour: new Date(now.getTime() - 8 * 24 * 60 * 60 * 1000), numNewConnections: 30 }, // 8 days ago (should be excluded)
    ];

    await NewConnectionsMetric.insertMany(metrics);

    const count = await NewConnectionsMetric.getNewConnectionsLast7Days();
    expect(count).to.equal(70); // 10 + 15 + 20 + 25 = 70 (excluding the 8-day-old metric)
  });

  it('should return 0 when no metrics exist', async () => {
    const count = await NewConnectionsMetric.getNewConnectionsLast7Days();
    expect(count).to.equal(0);
  });
});

describe('New Connections API', () => {
  beforeEach(async () => {
    await NewConnectionsMetric.deleteMany({});
  });

  it('should return new connections count via API', async () => {
    // Create some test metrics
    const now = new Date();
    const metrics = [
      { hour: new Date(now.getTime() - 1 * 60 * 60 * 1000), numNewConnections: 100 },
      { hour: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000), numNewConnections: 200 },
    ];
    await NewConnectionsMetric.insertMany(metrics);

    // Initialize a user for authentication
    const res1 = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 999)
      .send({ appVersion: '1.13.81' });
    expect(res1.status).to.equal(200);

    // Test the API endpoint
    const res = await request(app)
      .get('/v1/social-proof/new-connections')
      .set('authorization', 999);

    expect(res.status).to.equal(200);
    expect(res.body).to.have.property('newConnectionsCount');
    expect(res.body.newConnectionsCount).to.equal(300);
  });

  it('should return 0 when no connections exist', async () => {
    // Initialize a user for authentication
    const res1 = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 998)
      .send({ appVersion: '1.13.81' });
    expect(res1.status).to.equal(200);

    // Test the API endpoint with no data
    const res = await request(app)
      .get('/v1/social-proof/new-connections')
      .set('authorization', 998);

    expect(res.status).to.equal(200);
    expect(res.body).to.have.property('newConnectionsCount');
    expect(res.body.newConnectionsCount).to.equal(0);
  });
});
